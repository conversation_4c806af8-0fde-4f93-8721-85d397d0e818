import { Router } from 'express';
import { ProjectController } from '../controllers/ProjectController';
import { validate, validationSchemas, validateQuery, validateParams } from '../middleware/validation';
import { authenticateToken, rateLimitPerUser } from '../middleware/auth';
import { CSRFProtection } from '../middleware/security';

const router = Router();

// All project routes require authentication
router.use(authenticateToken);

// Create new project
router.post('/',
  validate(validationSchemas.createProject),
  rateLimitPerUser(10, 60 * 1000), // 10 project creations per minute
  CSRFProtection.protect(),
  ProjectController.createProject
);

// Get user projects
router.get('/',
  validateQuery(validationSchemas.pagination),
  ProjectController.getUserProjects
);

// Search projects
router.get('/search',
  validateQuery(validationSchemas.searchQuery),
  ProjectController.searchProjects
);

// Get specific project
router.get('/:projectId',
  validateParams(validationSchemas.projectId),
  ProjectController.getProject
);

// Update project
router.put('/:projectId',
  validateParams(validationSchemas.projectId),
  validate(validationSchemas.updateProject),
  CSRFProtection.protect(),
  ProjectController.updateProject
);

// Delete project
router.delete('/:projectId',
  validateParams(validationSchemas.projectId),
  CSRFProtection.protect(),
  ProjectController.deleteProject
);

// Get project threads
router.get('/:projectId/threads',
  validateParams(validationSchemas.projectId),
  validateQuery(validationSchemas.pagination),
  ProjectController.getProjectThreads
);

// Send message in project thread
router.post('/:projectId/message',
  validateParams(validationSchemas.projectId),
  validate(validationSchemas.projectMessage),
  rateLimitPerUser(20, 60 * 1000), // 20 messages per minute
  CSRFProtection.protect(),
  ProjectController.sendProjectMessage
);

// Send streaming message in project thread
router.post('/:projectId/message/stream',
  validateParams(validationSchemas.projectId),
  validate(validationSchemas.projectMessage),
  rateLimitPerUser(20, 60 * 1000), // 20 messages per minute
  CSRFProtection.protect(),
  ProjectController.sendProjectStreamingMessage
);

// Get project statistics
router.get('/:projectId/stats',
  validateParams(validationSchemas.projectId),
  ProjectController.getProjectStats
);

export default router;
