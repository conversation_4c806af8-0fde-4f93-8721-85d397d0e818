import { Router } from 'express';
import { ThreadController } from '../controllers/ThreadController';
import { validate, validationSchemas, validateQuery, validateParams } from '../middleware/validation';
import { authenticateToken, rateLimitPerUser } from '../middleware/auth';
import { CSRFProtection } from '../middleware/security';

const router = Router();

// All thread routes require authentication
router.use(authenticateToken);

// Send message in thread (regular or project)
router.post('/message',
  validate(validationSchemas.threadMessage),
  rateLimitPerUser(20, 60 * 1000), // 20 messages per minute
  CSRFProtection.protect(),
  ThreadController.sendMessage
);

// Send streaming message in thread (regular or project)
router.post('/message/stream',
  validate(validationSchemas.threadMessage),
  rateLimitPerUser(20, 60 * 1000), // 20 messages per minute
  CSRFProtection.protect(),
  ThreadController.sendStreamingMessage
);

// Get user threads (regular threads only, not project threads)
router.get('/',
  validateQuery(validationSchemas.pagination),
  ThreadController.getUserThreads
);

// Get specific thread details
router.get('/:threadId',
  validateParams(validationSchemas.threadId),
  ThreadController.getThread
);

// Get thread messages
router.get('/:threadId/messages',
  validateParams(validationSchemas.threadId),
  validateQuery(validationSchemas.pagination),
  ThreadController.getThreadMessages
);

// Update thread name
router.put('/:threadId/name',
  validateParams(validationSchemas.threadId),
  validate(validationSchemas.updateThreadName),
  ThreadController.updateThreadName
);

// Delete thread
router.delete('/:threadId',
  validateParams(validationSchemas.threadId),
  ThreadController.deleteThread
);

export default router;
