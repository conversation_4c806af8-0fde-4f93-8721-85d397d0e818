import { Response } from 'express';
import { ThreadService, ThreadChatRequest } from '../services/ThreadService';
import { ResponseUtil } from '../utils/response';
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants';
import { AuthenticatedRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../config/logger';

export class ThreadController {
  /**
   * Send message in a thread (regular or project)
   */
  static sendMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const chatRequest: ThreadChatRequest = req.body;
    const result = await ThreadService.processThreadChat(req.user.userId, chatRequest);
    
    ResponseUtil.success(res, SUCCESS_MESSAGES.MESSAGE_SENT, result);
  });

  /**
   * Send streaming message in a thread (regular or project)
   */
  static sendStreamingMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const chatRequest: ThreadChatRequest = req.body;

    try {
      const result = await ThreadService.processThreadChatStreaming(req.user.userId, chatRequest, res);
      // Note: Response is handled by the streaming service, no need to send additional response
    } catch (error) {
      logger.error('Error in streaming thread chat:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });

  /**
   * Get user threads (regular threads only, not project threads)
   */
  static getUserThreads = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const limit = parseInt(req.query.limit as string) || 6;
    const offset = parseInt(req.query.offset as string) || 0;

    const threads = await ThreadService.getUserThreads(req.user.userId, limit, offset);
    
    ResponseUtil.success(res, 'Threads retrieved successfully', {
      threads,
      limit,
      offset,
    });
  });

  /**
   * Get thread messages
   */
  static getThreadMessages = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { threadId } = req.params;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    if (!threadId) {
      ResponseUtil.badRequest(res, 'Thread ID is required');
      return;
    }

    const messages = await ThreadService.getThreadMessages(
      threadId,
      req.user.userId,
      limit,
      offset
    );

    ResponseUtil.success(res, 'Messages retrieved successfully', {
      messages,
      threadId,
      limit,
      offset,
    });
  });

  /**
   * Update thread name
   */
  static updateThreadName = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { threadId } = req.params;
    const { name } = req.body;

    if (!threadId) {
      ResponseUtil.badRequest(res, 'Thread ID is required');
      return;
    }

    if (!name || name.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Thread name is required');
      return;
    }

    if (name.length > 255) {
      ResponseUtil.badRequest(res, 'Thread name is too long (max 255 characters)');
      return;
    }

    await ThreadService.updateThreadName(threadId, req.user.userId, name.trim());
    
    ResponseUtil.success(res, 'Thread name updated successfully');
  });

  /**
   * Delete thread
   */
  static deleteThread = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { threadId } = req.params;

    if (!threadId) {
      ResponseUtil.badRequest(res, 'Thread ID is required');
      return;
    }

    // Find and verify thread ownership
    const { ChatThread } = await import('../models/chat');
    const thread = await ChatThread.findByIdAndUser(threadId, req.user.userId);
    
    if (!thread) {
      ResponseUtil.notFound(res, 'Thread not found');
      return;
    }

    // If it's a project thread, delete from Pinecone
    if (thread.projectId) {
      const { PineconeService } = await import('../services/PineconeService');
      await PineconeService.deleteThreadMessages(thread.projectId, threadId);
    }

    // Delete thread and associated messages
    await thread.destroy();

    logger.info(`Thread deleted: ${threadId} by user ${req.user.userId}`);
    
    ResponseUtil.success(res, 'Thread deleted successfully');
  });

  /**
   * Get thread details
   */
  static getThread = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { threadId } = req.params;

    if (!threadId) {
      ResponseUtil.badRequest(res, 'Thread ID is required');
      return;
    }

    const { ChatThread } = await import('../models/chat');
    const thread = await ChatThread.findByIdAndUser(threadId, req.user.userId);
    
    if (!thread) {
      ResponseUtil.notFound(res, 'Thread not found');
      return;
    }

    // Get message count
    const { ChatMessage } = await import('../models/chat');
    const messageCount = await ChatMessage.count({
      where: { chatId: threadId }
    });

    const threadData = {
      ...thread.toJSON(),
      messageCount,
    };

    ResponseUtil.success(res, 'Thread retrieved successfully', threadData);
  });
}
