import { ChatThread, ChatMessage, Project } from '../models/chat';
import { LLMService } from './LLMService';
import { PineconeService } from './PineconeService';
import { CreditService } from './CreditService';
import { EncryptionUtil } from '../utils/encryption';
import { CREDIT_SYSTEM, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants';
import logger from '../config/logger';
import { Response } from 'express';

export interface ThreadChatRequest {
  message: string;
  threadId?: string;
  projectId?: string;
  llmModel?: string;
}

export interface ThreadChatResponse {
  response: string;
  threadId: string;
  sessionId: string;
  messageId: string;
  isProject: boolean;
  projectId?: string;
}

export class ThreadService {
  /**
   * Process chat message in a thread (regular or project)
   */
  static async processThreadChat(
    userId: string,
    chatRequest: ThreadChatRequest
  ): Promise<ThreadChatResponse> {
    try {
      const { message, threadId, projectId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let thread: ChatThread;
      let project: Project | null = null;

      // Handle project chat
      if (projectId) {
        project = await Project.findByIdAndUser(projectId, userId);
        if (!project) {
          throw new Error('Project not found');
        }

        if (threadId) {
          // Find existing project thread by ID
          const existingThread = await ChatThread.findByIdAndUser(threadId, userId);
          if (!existingThread || existingThread.projectId !== projectId) {
            throw new Error('Thread not found in project');
          }
          thread = existingThread;
        } else {
          // Create new project thread
          const newSessionId = EncryptionUtil.generateSessionId();
          thread = await ChatThread.createThread({
            userId,
            projectId,
            sessionId: newSessionId,
            isGuest: false,
          });
        }
      } else {
        // Handle regular chat thread
        if (threadId) {
          // Find existing regular thread
          const existingThread = await ChatThread.findByIdAndUser(threadId, userId);
          if (!existingThread || existingThread.projectId !== null) {
            throw new Error('Thread not found');
          }
          thread = existingThread;
        } else {
          // Create new regular thread
          const newSessionId = EncryptionUtil.generateSessionId();
          thread = await ChatThread.createThread({
            userId,
            sessionId: newSessionId,
            isGuest: false,
          });
        }
      }

      // Generate response using LLM
      const conversationHistory = await ThreadService.getThreadHistory(thread.id);
      let systemPrompt = this.getSystemPrompt();

      // For project chats, include project context
      if (project) {
        systemPrompt = this.getProjectSystemPrompt(project.description, project.rules);
        
        // Get additional context from Pinecone for project chats
        const pineconeContext = await PineconeService.getConversationContext(
          project.id,
          thread.id,
          message,
          3
        );
        
        if (pineconeContext.length > 0) {
          systemPrompt += '\n\nRelevant previous conversations:\n' + pineconeContext.join('\n\n');
        }
      }

      const response = await LLMService.generateResponse(
        message,
        llmModel,
        systemPrompt,
        conversationHistory
      );

      // Save message and response
      const chatMessage = await ChatMessage.createMessage({
        chatId: thread.id,
        message,
        response,
        llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
      });

      // Store in Pinecone for project chats
      if (project) {
        await PineconeService.storeMessage(
          project.id,
          thread.id,
          chatMessage.id,
          message,
          response,
          {
            userId,
            projectName: project.name,
            llmModel: chatMessage.llmModel,
          }
        );
      }

      // Deduct credit
      const creditDeducted = await CreditService.deductCredits(
        userId,
        CREDIT_SYSTEM.CHAT_MESSAGE_COST,
        CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
      );
      
      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after processing message`);
      }

      // Update thread name if it's the first message
      if (!thread.name) {
        const threadName = thread.generateThreadName(message);
        await thread.updateName(threadName);
      }

      logger.info(`Thread chat message processed: ${thread.id}, project: ${!!project}`);

      return {
        response,
        threadId: thread.id,
        sessionId: thread.sessionId, // Include sessionId for frontend compatibility
        messageId: chatMessage.id,
        isProject: !!project,
        projectId: project?.id,
      };
    } catch (error) {
      logger.error('Error processing thread chat:', error);
      throw error;
    }
  }

  /**
   * Process streaming chat message in a thread (regular or project)
   */
  static async processThreadChatStreaming(
    userId: string,
    chatRequest: ThreadChatRequest,
    res: Response
  ): Promise<{ threadId: string; sessionId: string; messageId: string; isProject: boolean; projectId?: string }> {
    try {
      const { message, threadId, projectId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let thread: ChatThread;
      let project: Project | null = null;

      // Handle project chat
      if (projectId) {
        project = await Project.findByIdAndUser(projectId, userId);
        if (!project) {
          throw new Error('Project not found');
        }

        if (threadId) {
          // Find existing project thread by ID
          const existingThread = await ChatThread.findByIdAndUser(threadId, userId);
          if (!existingThread || existingThread.projectId !== projectId) {
            throw new Error('Thread not found in project');
          }
          thread = existingThread;
        } else {
          // Create new project thread
          const newSessionId = EncryptionUtil.generateSessionId();
          thread = await ChatThread.createThread({
            userId,
            projectId,
            sessionId: newSessionId,
            isGuest: false,
          });
        }
      } else {
        // Handle regular thread
        if (threadId) {
          // Find existing thread by ID
          const existingThread = await ChatThread.findByIdAndUser(threadId, userId);
          if (!existingThread) {
            throw new Error('Thread not found');
          }
          thread = existingThread;
        } else {
          // Create new thread
          const newSessionId = EncryptionUtil.generateSessionId();
          thread = await ChatThread.createThread({
            userId,
            sessionId: newSessionId,
            isGuest: false,
          });
        }
      }

      // Generate streaming response using LLM
      const conversationHistory = await ThreadService.getThreadHistory(thread.id);
      let systemPrompt = this.getSystemPrompt();

      // For project chats, include project context
      if (project) {
        systemPrompt = this.getProjectSystemPrompt(project.description, project.rules);

        // Get additional context from Pinecone for project chats
        const pineconeContext = await PineconeService.getConversationContext(
          project.id,
          thread.id,
          message,
          3
        );

        if (pineconeContext.length > 0) {
          systemPrompt += '\n\nRelevant previous conversations:\n' + pineconeContext.join('\n\n');
        }
      }

      const response = await LLMService.generateStreamingResponse(
        message,
        res,
        llmModel,
        systemPrompt,
        conversationHistory
      );

      // Save message and response
      const chatMessage = await ChatMessage.createMessage({
        chatId: thread.id,
        message,
        response,
        llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
      });

      // Store in Pinecone for project chats
      if (project) {
        await PineconeService.storeMessage(
          project.id,
          thread.id,
          chatMessage.id,
          message,
          response,
          {
            userId,
            projectName: project.name,
            llmModel: chatMessage.llmModel,
          }
        );
      }

      // Deduct credit
      const creditDeducted = await CreditService.deductCredits(
        userId,
        CREDIT_SYSTEM.CHAT_MESSAGE_COST,
        CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
      );

      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after processing message`);
      }

      // Update thread name if it's the first message
      if (!thread.name) {
        const threadName = thread.generateThreadName(message);
        await thread.updateName(threadName);
      }

      logger.info(`Thread streaming chat message processed: ${thread.id}, project: ${!!project}`);

      return {
        threadId: thread.id,
        sessionId: thread.sessionId, // Include sessionId for frontend compatibility
        messageId: chatMessage.id,
        isProject: !!project,
        projectId: project?.id,
      };
    } catch (error) {
      logger.error('Error processing thread streaming chat:', error);
      throw error;
    }
  }

  /**
   * Get thread conversation history (last 5 messages for RAG)
   */
  static async getThreadHistory(threadId: string): Promise<Array<{ role: 'user' | 'assistant'; content: string }>> {
    try {
      const messages = await ChatMessage.findChatMessages(threadId, 5, 0);
      
      return messages.map(msg => [
        { role: 'user' as const, content: msg.message },
        { role: 'assistant' as const, content: msg.response }
      ]).flat();
    } catch (error) {
      logger.error('Error getting thread history:', error);
      return [];
    }
  }

  /**
   * Get user threads with pagination
   */
  static async getUserThreads(userId: string, limit: number = 6, offset: number = 0): Promise<ChatThread[]> {
    return ChatThread.findUserThreads(userId, limit, offset);
  }

  /**
   * Get project threads with pagination
   */
  static async getProjectThreads(projectId: string, limit: number = 6, offset: number = 0): Promise<ChatThread[]> {
    return ChatThread.findProjectThreads(projectId, limit, offset);
  }

  /**
   * Update thread name
   */
  static async updateThreadName(threadId: string, userId: string, name: string): Promise<void> {
    const thread = await ChatThread.findByIdAndUser(threadId, userId);
    if (!thread) {
      throw new Error('Thread not found');
    }
    
    await thread.updateName(name);
  }

  /**
   * Get thread messages
   */
  static async getThreadMessages(
    threadId: string,
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<ChatMessage[]> {
    // Verify thread belongs to user
    const thread = await ChatThread.findByIdAndUser(threadId, userId);
    if (!thread) {
      throw new Error('Thread not found');
    }

    return ChatMessage.findChatMessages(threadId, limit, offset);
  }

  /**
   * Get system prompt for regular chats
   */
  private static getSystemPrompt(): string {
    return `You are a helpful AI assistant. Provide accurate, helpful, and concise responses to user queries.`;
  }

  /**
   * Get system prompt for project chats
   */
  private static getProjectSystemPrompt(description: string, rules: string): string {
    return `You are an AI assistant working on a specific project.

Project Description: ${description}

Project Rules: ${rules}

Please follow the project rules and context when responding to user queries. Provide responses that are relevant to the project scope and objectives.`;
  }
}
