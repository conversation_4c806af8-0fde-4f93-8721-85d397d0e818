#!/usr/bin/env node

/**
 * Test script for streaming API conversation continuity
 * Run with: node test-streaming.js
 */

const BASE_URL = 'http://localhost:3000/api';
const AUTH_TOKEN = 'YOUR_AUTH_TOKEN_HERE'; // Replace with actual token

async function testChatConversation() {
  console.log('🧪 Testing Chat Conversation Continuity...\n');

  // First message - should create new conversation
  console.log('1️⃣ Sending first message...');
  const firstResponse = await fetch(`${BASE_URL}/chat/message`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`
    },
    body: JSON.stringify({
      message: 'Hello, my name is <PERSON>. Remember this.'
    })
  });

  const firstResult = await firstResponse.json();
  console.log('First response sessionId:', firstResult.data?.sessionId);
  
  if (!firstResult.data?.sessionId) {
    console.error('❌ No sessionId returned from first message');
    return;
  }

  const sessionId = firstResult.data.sessionId;

  // Second message - should continue conversation
  console.log('\n2️⃣ Sending second message with sessionId...');
  const secondResponse = await fetch(`${BASE_URL}/chat/message`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`
    },
    body: JSON.stringify({
      message: 'What is my name?',
      sessionId: sessionId
    })
  });

  const secondResult = await secondResponse.json();
  console.log('Second response:', secondResult.data?.response);
  
  if (secondResult.data?.response?.toLowerCase().includes('john')) {
    console.log('✅ Chat conversation continuity working!');
  } else {
    console.log('❌ Chat conversation continuity failed - AI didn\'t remember the name');
  }
}

async function testThreadConversation() {
  console.log('\n🧪 Testing Thread Conversation Continuity...\n');

  // First message - should create new thread
  console.log('1️⃣ Sending first thread message...');
  const firstResponse = await fetch(`${BASE_URL}/threads/message`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`
    },
    body: JSON.stringify({
      message: 'Hello, my favorite color is blue. Remember this.'
    })
  });

  const firstResult = await firstResponse.json();
  console.log('First response threadId:', firstResult.data?.threadId);
  
  if (!firstResult.data?.threadId) {
    console.error('❌ No threadId returned from first message');
    return;
  }

  const threadId = firstResult.data.threadId;

  // Second message - should continue thread
  console.log('\n2️⃣ Sending second message with threadId...');
  const secondResponse = await fetch(`${BASE_URL}/threads/message`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`
    },
    body: JSON.stringify({
      message: 'What is my favorite color?',
      threadId: threadId
    })
  });

  const secondResult = await secondResponse.json();
  console.log('Second response:', secondResult.data?.response);
  
  if (secondResult.data?.response?.toLowerCase().includes('blue')) {
    console.log('✅ Thread conversation continuity working!');
  } else {
    console.log('❌ Thread conversation continuity failed - AI didn\'t remember the color');
  }
}

async function testStreamingChat() {
  console.log('\n🧪 Testing Streaming Chat...\n');

  console.log('1️⃣ Sending streaming message...');
  const response = await fetch(`${BASE_URL}/chat/message/stream`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`
    },
    body: JSON.stringify({
      message: 'Tell me a short joke'
    })
  });

  if (!response.body) {
    console.error('❌ No response body for streaming');
    return;
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let buffer = '';
  let fullResponse = '';

  console.log('📡 Streaming response:');
  
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    buffer += decoder.decode(value, { stream: true });
    const lines = buffer.split('\n');
    buffer = lines.pop() || '';

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        try {
          const data = JSON.parse(line.slice(6));
          
          if (data.type === 'chunk') {
            process.stdout.write(data.content);
            fullResponse += data.content;
          } else if (data.type === 'complete') {
            console.log('\n✅ Streaming completed successfully!');
            return;
          } else if (data.type === 'error') {
            console.log('\n❌ Streaming error:', data.error);
            return;
          }
        } catch (e) {
          // Ignore parse errors
        }
      }
    }
  }
}

async function main() {
  console.log('🚀 Starting Streaming API Tests\n');
  console.log('Make sure your server is running on http://localhost:3000');
  console.log('Update AUTH_TOKEN in this script with a valid token\n');

  try {
    await testChatConversation();
    await testThreadConversation();
    await testStreamingChat();
    
    console.log('\n🎉 All tests completed!');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

if (require.main === module) {
  main();
}
